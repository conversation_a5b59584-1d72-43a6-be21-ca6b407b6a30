I'm building a project.

Idea: to help students with their projects. We give training, then a mini project followed by major project.

Users: <PERSON><PERSON>, Student, Trainer, College Staff, Parent

Responsibilities:
Admin can register students, trainers, colleges, staff and parents.
Admin can assign students to trainers.
Admin can assign colleges to students.
Admin can assign staff to colleges.
Admin can assign parents to students.
Admin can view all users.
Admin can view all projects.
Admin can view all trainings.
Admin can view all payments.
<PERSON>min can view all reports.
<PERSON>min can view all settings.
<PERSON>min can view all notifications.
<PERSON>min can view all messages.
<PERSON>min can view all logs.
Admin can view all analytics.
Admin can view all audits.

Trainer can view all students assigned to them.
Trainer can view all projects assigned to them.
Trainer can view all trainings assigned to them.
Trainer can view all payments assigned to them.
Trainer can view all reports assigned to them.
Trainer can view all settings assigned to them.
Trainer can view all notifications assigned to them.
Trainer can view all messages assigned to them.
Trainer can view all logs assigned to them.
Trainer can view all analytics assigned to them.
Trainer can view all audits assigned to them.

Student can view all projects assigned to them.
Student can view all trainings assigned to them.
Student can view all payments assigned to them.
Student can view all reports assigned to them.
Student can view all settings assigned to them.
Student can view all notifications assigned to them.
Student can view all messages assigned to them.
Student can view all logs assigned to them.
Student can view all analytics assigned to them.
Student can view all audits assigned to them.

College Staff can view all students assigned to them.
College Staff can view all projects assigned to them.
College Staff can view all trainings assigned to them.
College Staff can view all payments assigned to them.
College Staff can view all reports assigned to them.
College Staff can view all settings assigned to them.
College Staff can view all notifications assigned to them.
College Staff can view all messages assigned to them.
College Staff can view all logs assigned to them.
College Staff can view all analytics assigned to them.
College Staff can view all audits assigned to them.

Parent can view all students assigned to them.
Parent can view all projects assigned to them.
Parent can view all trainings assigned to them.
Parent can view all payments assigned to them.
Parent can view all reports assigned to them.
Parent can view all settings assigned to them.
Parent can view all notifications assigned to them.
Parent can view all messages assigned to them.
Parent can view all logs assigned to them.
Parent can view all analytics assigned to them.
Parent can view all audits assigned to them.


Landing page:

Services We Offer
service-icon1
Project Tracking
Monitor tasks, milestones, and deadlines in real time to keep projects on track and teams aligned.

service-icon2
Training Contributions
Measure and showcase the impact of learning sessions, mentorship, and knowledge-sharing.

service-icon3
Institutes & Faculty Connect
Strengthen collaboration between institutes and faculty for impactful learning and student growth.

service-icon4
Parent Dashboard
Stay informed about your child’s progress, performance, and learning activities in one place.

service-icon5
Placement Analytics
Gain insights into placement trends, success rates, and career outcomes to boost employability.

service-icon6
Smart Notifications
Stay updated with personalized alerts on classes, projects, placements, and announcements.

